import React from 'react';

const FONT_OPTIONS = [
  { label: 'Inter', value: 'inter' },
  { label: '<PERSON><PERSON><PERSON>', value: 'nunito' },
  { label: 'Poppins', value: 'poppins' },
  { label: 'Quicksand', value: 'quicksand' },
  { label: 'Work Sans', value: 'worksans' },
];

export default function FontSwitcher({ currentFont, onChange }) {
  return (
    <div className="bg-yellow-50 border border-yellow-100 rounded-lg p-3 mb-3 shadow-sm">
      <label htmlFor="font-switcher" className="block text-xs font-bold text-yellow-700 mb-1 tracking-wide uppercase">
        Font Family
      </label>
      <select
        id="font-switcher"
        value={currentFont}
        onChange={e => onChange(e.target.value)}
        className="w-full border border-yellow-200 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-400 bg-white transition-all font-semibold text-yellow-700 hover:border-orange-400"
      >
        {FONT_OPTIONS.map(opt => (
          <option key={opt.value} value={opt.value} className={`font-${opt.value}`}>{opt.label}</option>
        ))}
      </select>
      <div className="mt-2 text-xs text-gray-500">Preview: <span className={`font-${currentFont}`}>{FONT_OPTIONS.find(f => f.value === currentFont)?.label}</span></div>
    </div>
  );
}
