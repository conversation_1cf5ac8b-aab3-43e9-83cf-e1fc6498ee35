// Test script to verify HPP error fixes
import { ensureArray, sanitizeHPPRecord, validateHPPRecord } from '../utils/errorHandling.js';

// Test cases that would previously cause errors
const testCases = [
  // Case 1: undefined ingredients
  {
    id: '1',
    productName: 'Test Product 1',
    ingredients: undefined,
    hpp: 1000
  },
  
  // Case 2: null ingredients
  {
    id: '2',
    productName: 'Test Product 2',
    ingredients: null,
    hpp: 2000
  },
  
  // Case 3: non-array ingredients
  {
    id: '3',
    productName: 'Test Product 3',
    ingredients: 'not an array',
    hpp: 3000
  },
  
  // Case 4: empty object
  {},
  
  // Case 5: null record
  null,
  
  // Case 6: valid record
  {
    id: '6',
    productName: 'Valid Product',
    ingredients: [
      { ingredientId: 1, quantity: 100, unit: 'g', harga: 500 },
      { ingredientId: 2, quantity: 50, unit: 'ml', harga: 300 }
    ],
    hpp: 800
  }
];

console.log('Testing HPP error handling...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test Case ${index + 1}:`);
  console.log('Input:', testCase);
  
  try {
    // Test validation
    const isValid = validateHPPRecord(testCase);
    console.log('Valid:', isValid);
    
    // Test sanitization
    const sanitized = sanitizeHPPRecord(testCase);
    console.log('Sanitized:', sanitized);
    
    // Test array access (this would previously cause the error)
    const ingredients = ensureArray(sanitized.ingredients);
    console.log('Ingredients length:', ingredients.length);
    
    // Test the specific operation that was failing
    if (ingredients.length > 2) {
      console.log('Would show +' + (ingredients.length - 2) + ' more ingredients');
    }
    
    console.log('✅ Test passed\n');
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    console.log('');
  }
});

console.log('All tests completed!');
