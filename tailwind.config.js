/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: [
          'Inter',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ],
        nunito: [
          'Nunito',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ],
        poppins: [
          'Poppins',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ],
        quicksand: [
          'Quicksand',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ],
        worksans: [
          'Work Sans',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ],
        sans: [
          'Poppins',
          'ui-sans-serif', 'system-ui',
          '-apple-system', 'BlinkMacSystemFont',
          'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial',
          'Noto Sans', 'sans-serif'
        ]
      },
      colors: {
        theme: {
          50: 'var(--theme-50)',
          100: 'var(--theme-100)',
          200: 'var(--theme-200)',
          300: 'var(--theme-300)',
          400: 'var(--theme-400)',
          500: 'var(--theme-500)',
          600: 'var(--theme-600)',
          700: 'var(--theme-700)',
          800: 'var(--theme-800)',
          900: 'var(--theme-900)',
        },
        'theme-secondary': {
          50: 'var(--theme-secondary-50)',
          100: 'var(--theme-secondary-100)',
          200: 'var(--theme-secondary-200)',
          300: 'var(--theme-secondary-300)',
          400: 'var(--theme-secondary-400)',
          500: 'var(--theme-secondary-500)',
          600: 'var(--theme-secondary-600)',
          700: 'var(--theme-secondary-700)',
          800: 'var(--theme-secondary-800)',
          900: 'var(--theme-secondary-900)',
        },
      },
    },
  },
  plugins: [],
}
