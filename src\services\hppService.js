import { supabase } from './supabaseClient'
import { v4 as uuidv4 } from 'uuid'

class HPPService {
  constructor() {
    this.storageKey = 'hpp_records';
    this.records = this.loadRecords();
  }

  loadRecords() {
    const savedRecords = localStorage.getItem(this.storageKey);
    return savedRecords ? JSON.parse(savedRecords) : [];
  }

  saveRecords() {
    localStorage.setItem(this.storageKey, JSON.stringify(this.records));
  }

  async getAllRecords() {
    const { data, error } = await supabase.from('hpp').select('*')
    if (error) {
      console.error('Supabase getAllRecords error:', error)
      return []
    }
    return data
  }

  async getRecordById(id) {
    const { data, error } = await supabase.from('hpp').select('*').eq('id', id).single()
    if (error) {
      console.error('Supabase getRecordById error:', error)
      return null
    }
    return data
  }

  async addRecord(record) {
    // Only keep valid columns for hpp table
    const { produk_id, bahan_baku_id, jumlah, harga } = record;
    const newRecord = {
      id: uuidv4(),
      produk_id,
      bahan_baku_id,
      jumlah,
      harga,
      createdat: new Date().toISOString() // use lowercase to match table
    }
    const { data, error } = await supabase.from('hpp').insert([newRecord]).select()
    if (error) {
      console.error('Supabase addRecord error:', error)
      return null
    }
    return data[0]
  }

  async updateRecord(id, updates) {
    const { data, error } = await supabase.from('hpp').update(updates).eq('id', id).select()
    if (error) {
      console.error('Supabase updateRecord error:', error)
      return null
    }
    return data[0]
  }

  async deleteRecord(id) {
    const { error } = await supabase.from('hpp').delete().eq('id', id)
    if (error) {
      console.error('Supabase deleteRecord error:', error)
      return false
    }
    return true
  }

  searchRecords(query = '') {
    const searchLower = query.toLowerCase();
    return this.records.filter(record => 
      record.productName.toLowerCase().includes(searchLower)
    );
  }
}

// Export a singleton instance
export const hppService = new HPPService();
