import React from "react";
import {
	MapPin,
	Phone,
	Mail,
	Clock,
	Award,
	Users,
	ShoppingBag,
	Heart,
	Star,
	CheckCircle,
	MessageCircle,
} from "lucide-react";
import ModernButton from '../components/ModernButton';

const About = () => {
	const businessInfo = {
		name: "<PERSON>ot<PERSON>gil",
		tagline: "<PERSON><PERSON><PERSON>, Bolu dan Snack",
		description:
			"Roti Ragil adalah usaha keluarga yang telah berdedikasi menghadirkan roti dan kue berkualitas tinggi dengan cita rasa tradisional yang autentik. Kami menggunakan bahan-bahan pilihan dan resep turun temurun untuk menciptakan produk yang tidak hanya lezat, tetapi juga bergizi.",

		contact: {
			address:
				"Jl. Gatotkaca No. 15\nWIROBRAJAN, KOTA YOGYAKARTA, DI YOGYAKARTA, ID 55252",
			phone: "+62 895-4026-52626",
			whatsapp: "+62 895-4026-52626",
			email: "<EMAIL>",
			website: "www.rotiragil.com",
		},

		business: {
			pirt: "2053471011676-30",
			established: "2022",
			owner: "Anindya Wulandari",
			employees: "2 orang",
			dailyProduction: "200-300 pcs",
		},

		operatingHours: {
			weekdays: "05:00 - 20:00 WIB",
			weekend: "05:00 - 21:00 WIB",
			production: "03:00 - 06:00 WIB",
		},

		products: [
			"Roti Sosis",
			"Roti Meses",
			"Bolu Gulung",
			"Chiffon Keju",
			"Roti Abon",
			"Roti Pisang",
			"Floss Roll",
			"Roti Kacang Hijau/Merah",
			"Roti Keju",
		],

		achievements: [
			"Sertifikat PIRT dari Dinas Kesehatan",
			"Sertifikat Halal MUI",
			"Member Asosiasi Pengusaha Roti Indonesia",
		],
	};

	const ICON_CLASS = "h-6 w-6 mr-2";
	const ICON_SECONDARY_CLASS = "h-5 w-5 mr-2";
	const ICON_ACCENT_CLASS = "h-5 w-5 flex-shrink-0";

	return (
		<div className="space-y-8">
			{/* Header */}
			<div className="text-center">
				<h1
					className="text-3xl md:text-4xl font-bold mb-4"
					style={{
						background: "var(--theme-gradient)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
					}}
				>
					Tentang {businessInfo.name}
				</h1>
				<p className="text-xl text-gray-600 font-medium">
					{businessInfo.tagline}
				</p>
			</div>

			{/* Hero Section */}
			<div className="card">
				<div className="p-8">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
						<div>
							<h2 className="text-2xl font-bold text-gray-900 mb-4">
								Cerita Kami
							</h2>
							<p className="text-gray-600 leading-relaxed mb-6">
								{businessInfo.description}
							</p>
							<div className="grid grid-cols-2 gap-4">
								<div
									className="text-center p-4 rounded-lg"
									style={{ backgroundColor: "var(--theme-50)" }}
								>
									<div
										className="text-2xl font-bold"
										style={{ color: "var(--theme-600)" }}
									>
										{businessInfo.business.established}
									</div>
									<div className="text-sm text-gray-600">Tahun Berdiri</div>
								</div>
								<div
									className="text-center p-4 rounded-lg"
									style={{ backgroundColor: "var(--theme-50)" }}
								>
									<div
										className="text-2xl font-bold"
										style={{ color: "var(--theme-600)" }}
									>
										{businessInfo.business.employees}
									</div>
									<div className="text-sm text-gray-600">Karyawan</div>
								</div>
							</div>
						</div>
						<div
							className="rounded-lg p-8 text-center"
							style={{ backgroundColor: "var(--theme-50)" }}
						>
							<div
								className="w-24 h-24 rounded-full mx-auto mb-4 flex items-center justify-center"
								style={{ background: "var(--theme-gradient)" }}
							>
								<ShoppingBag className="h-12 w-12 text-white" />
							</div>
							<h3 className="text-xl font-bold text-gray-900 mb-2">
								Produksi Harian
							</h3>
							<p
								className="text-3xl font-bold"
								style={{ color: "var(--theme-600)" }}
							>
								{businessInfo.business.dailyProduction}
							</p>
							<p className="text-gray-600">Roti & Kue Segar</p>
						</div>
					</div>
				</div>
			</div>

			{/* Contact Information */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div className="card">
					<div className="card-header">
						<h2 className="text-xl font-bold text-gray-900 flex items-center">
							<Phone
								className={ICON_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							Informasi Kontak
						</h2>
					</div>
					<div className="p-6 space-y-4">
						<div className="flex items-start space-x-3">
							<MapPin
								className={ICON_SECONDARY_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							<div>
								<p className="font-medium text-gray-900">Alamat</p>
								<p className="text-gray-600 whitespace-pre-line">
									{businessInfo.contact.address}
								</p>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<Phone
								className={ICON_SECONDARY_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							<div>
								<p className="font-medium text-gray-900">Telepon</p>
								<a
									href={`tel:${businessInfo.contact.phone}`}
									className="text-theme-600 hover:opacity-80"
								>
									{businessInfo.contact.phone}
								</a>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<MessageCircle
								className={ICON_SECONDARY_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							<div>
								<p className="font-medium text-gray-900">WhatsApp</p>
								<a
									href={`https://wa.me/${businessInfo.contact.whatsapp.replace(
										/[^0-9]/g,
										""
									)}`}
									target="_blank"
									rel="noopener noreferrer"
									className="text-theme-600 hover:opacity-80"
								>
									{businessInfo.contact.whatsapp}
								</a>
							</div>
						</div>

						<div className="flex items-center space-x-3">
							<Mail
								className={ICON_SECONDARY_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							<div>
								<p className="font-medium text-gray-900">Email</p>
								<a
									href={`mailto:${businessInfo.contact.email}`}
									className="text-theme-600 hover:opacity-80"
								>
									{businessInfo.contact.email}
								</a>
							</div>
						</div>
					</div>
				</div>

				<div className="card">
					<div className="card-header">
						<h2 className="text-xl font-bold text-gray-900 flex items-center">
							<Clock
								className={ICON_CLASS}
								style={{ color: "var(--theme-500)" }}
							/>
							Jam Operasional
						</h2>
					</div>
					<div className="p-6 space-y-4">
						<div className="flex justify-between items-center py-2 border-b border-gray-100">
							<span className="font-medium text-gray-900">Senin - Jumat</span>
							<span className="text-gray-600">
								{businessInfo.operatingHours.weekdays}
							</span>
						</div>
						<div className="flex justify-between items-center py-2 border-b border-gray-100">
							<span className="font-medium text-gray-900">Sabtu - Minggu</span>
							<span className="text-gray-600">
								{businessInfo.operatingHours.weekend}
							</span>
						</div>
						<div className="flex justify-between items-center py-2">
							<span className="font-medium text-gray-900">Produksi</span>
							<span className="text-gray-600">
								{businessInfo.operatingHours.production}
							</span>
						</div>

						<div
							className="mt-6 p-4 rounded-lg"
							style={{ backgroundColor: "var(--theme-50)" }}
						>
							<p className="text-sm" style={{ color: "var(--theme-800)" }}>
								<strong>Catatan:</strong> Untuk pesanan dalam jumlah besar,
								mohon hubungi kami H-1 untuk memastikan ketersediaan.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* CTA Section */}
			<div className="card" style={{ background: "linear-gradient(0deg, var(--theme-100, #f3f4f6) 90%, var(--theme-50, #fff) 100%)", boxShadow: "0 2px 24px rgba(0,0,0,0.04)", borderRadius: 18 }}>
				<div className="p-8 text-center">
					<h2 className="text-2xl font-bold mb-4" style={{ color: "var(--theme-900, #1e293b)" }}>Hubungi Kami Sekarang!</h2>
					<p className="mb-6" style={{ color: "var(--theme-700, #374151)" }}>
						Tertarik dengan produk kami? Jangan ragu untuk menghubungi kami
						untuk pemesanan atau konsultasi.
					</p>
					<div className="flex flex-wrap justify-center gap-4">
						<ModernButton
							as="a"
							href={`https://wa.me/${businessInfo.contact.whatsapp.replace(/[^0-9]/g, "")}`}
							target="_blank"
							rel="noopener noreferrer"
							color="primary"
							size="medium"
							style={{ borderRadius: 10, fontWeight: 600, minWidth: 120, minHeight: 40 }}
							startIcon={<MessageCircle className={ICON_SECONDARY_CLASS} />}
						>
							WhatsApp
						</ModernButton>
						<ModernButton
							as="a"
							href={`tel:${businessInfo.contact.phone}`}
							color="primary"
							size="medium"
							style={{ borderRadius: 10, fontWeight: 600, minWidth: 120, minHeight: 40 }}
							startIcon={<Phone className={ICON_SECONDARY_CLASS} />}
						>
							Telepon
						</ModernButton>
					</div>
				</div>
			</div>

			{/* Footer */}
			<div className="text-center py-6">
				<p className="text-gray-500 text-sm">
					© {new Date().getFullYear()} {businessInfo.name}. Dibuat dengan ❤️ untuk melayani pelanggan
					terbaik.
				</p>
			</div>
		</div>
	);
};

export default About;
