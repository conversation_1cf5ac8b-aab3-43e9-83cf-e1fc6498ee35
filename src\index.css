@import url('/fonts.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Select Custom Styles */
.react-select-container {
  @apply w-full;
}

.react-select__control {
  @apply border-gray-300 hover:border-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[42px] !important;
  box-shadow: none !important;
}

.react-select__control--is-focused {
  @apply border-blue-500 ring-2 ring-blue-200 !important;
}

.react-select__menu {
  @apply border border-gray-200 shadow-lg rounded-md !important;
  z-index: 100;
}

.react-select__option {
  @apply px-4 py-2 text-sm text-gray-800 cursor-pointer hover:bg-gray-100;
}

.react-select__option--is-selected {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.react-select__option--is-focused:not(.react-select__option--is-selected) {
  @apply bg-gray-100;
}

.react-select__single-value {
  @apply text-gray-900;
}

.react-select__placeholder {
  @apply text-gray-400;
}

.react-select__indicator-separator {
  @apply bg-gray-300;
}

.react-select__dropdown-indicator {
  @apply text-gray-400 hover:text-gray-500;
}

.react-select__clear-indicator {
  @apply text-gray-400 hover:text-red-500;
}

html, body {
  font-family: 'Poppins', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}


:root {
  font-family: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Theme System Variables */
  --theme-50: var(--color-50);
  --theme-100: var(--color-100);
  --theme-200: var(--color-200);
  --theme-300: var(--color-300);
  --theme-400: var(--color-400);
  --theme-500: var(--color-500);
  --theme-600: var(--color-600);
  --theme-700: var(--color-700);
  --theme-800: var(--color-800);
  --theme-900: var(--color-900);

  --theme-secondary-50: var(--secondary-50);
  --theme-secondary-100: var(--secondary-100);
  --theme-secondary-200: var(--secondary-200);
  --theme-secondary-300: var(--secondary-300);
  --theme-secondary-400: var(--secondary-400);
  --theme-secondary-500: var(--secondary-500);
  --theme-secondary-600: var(--secondary-600);
  --theme-secondary-700: var(--secondary-700);
  --theme-secondary-800: var(--secondary-800);
  --theme-secondary-900: var(--secondary-900);

  /* Default color variables for theme (gold/amber) */
  --color-50: #fffbea;
  --color-100: #fff3c4;
  --color-200: #fce588;
  --color-300: #fadb5f;
  --color-400: #f7c948;
  --color-500: #f0b429;
  --color-600: #de911d;
  --color-700: #cb6e17;
  --color-800: #b44d12;
  --color-900: #8d2b0b;

  /* Amber colors for gold theme */
  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;

  /* Yellow colors for secondary gold theme */
  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;

  /* Gradient for gold theme */
  --gradient-primary: linear-gradient(90deg, #FFD700 0%, #FFC107 100%);

  /* Gradient Variables */
  --theme-gradient: linear-gradient(90deg, var(--theme-gradient-from) 0%, var(--theme-gradient-to) 100%);
  --theme-gradient-hover: linear-gradient(90deg, var(--theme-gradient-hover-from) 0%, var(--theme-gradient-hover-to) 100%);

  /* Color System Variables */
  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;

  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;

  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;

  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7e22ce;
  --purple-800: #6b21a8;
  --purple-900: #581c87;

  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* For glassmorphism */
  background-attachment: fixed;
}

#root {
  width: 100%;
  min-height: 100vh;
  /* Slightly lighter for glassmorphism */
  background: transparent;
}

/* Glassmorphism utility */
.glass {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Modern shadow utility */
.shadow-modern {
  box-shadow: 0 4px 20px rgba(0,0,0,0.06), 0 1.5px 4px rgba(0,0,0,0.03);
}

/* Softer border radius globally for cards, containers, etc. */
.card, .container, .MuiPaper-root, .MuiDrawer-paper, .MuiAppBar-root {
  border-radius: 20px !important;
}


#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 text-white font-medium rounded-full shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background: var(--theme-gradient) !important;
    border: none;
    color: #fff !important;
    transition: all 0.2s ease;
  }
  .btn-primary:hover, .btn-primary:focus {
    background: var(--theme-gradient-hover) !important;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* For MUI/Material UI custom buttons */
  .MuiButton-containedPrimary, .MuiButton-root.btn-primary {
    background: var(--theme-gradient) !important;
    color: #fff !important;
    border: none;
    transition: all 0.2s ease;
  }
  .MuiButton-containedPrimary:hover, .MuiButton-root.btn-primary:hover, .MuiButton-root.btn-primary:focus {
    background: var(--theme-gradient-hover) !important;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 bg-white font-medium rounded-lg border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    color: var(--theme-primary);
  }

  .btn-danger {
    @apply inline-flex items-center px-4 py-2 text-white font-medium rounded-full shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background: linear-gradient(90deg, var(--red-500) 0%, var(--red-600) 100%) !important;
    border: none;
    color: #fff !important;
    transition: all 0.2s ease;
  }
  .btn-danger:hover, .btn-danger:focus {
    background: linear-gradient(90deg, var(--red-600) 0%, var(--red-700) 100%) !important;
    color: #fff !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 transition-colors duration-200;
    &:focus {
      --tw-ring-color: var(--theme-primary);
      border-color: var(--theme-primary);
    }
  }

  .mobile-card {
    @apply block md:hidden bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
  }

  /* Mobile-first responsive utilities */
  .responsive-table {
    width: 100%;
    overflow-x: auto;
    display: block;
  }
  .responsive-table table {
    width: 100%;
    min-width: 600px;
    border-collapse: collapse;
  }
  @media (max-width: 600px) {
    .responsive-table table {
      min-width: 480px;
      font-size: 14px;
    }
    .responsive-table th,
    .responsive-table td {
      padding: 10px 6px !important;
    }
    .responsive-table thead {
      font-size: 13px;
    }
  }

  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-nav-item {
    @apply flex items-center px-4 py-3 text-base font-medium rounded-lg mb-2 transition-all duration-200;
    min-height: 48px; /* Larger touch target for mobile */
  }

  .mobile-nav-item:hover {
    background: rgba(0,0,0,0.08) !important;
  }

  .mobile-nav-item.active {
    background: var(--theme-gradient);
    color: white;
  }

  /* Mobile-specific button styles */
  @media (max-width: 768px) {
    .btn-primary {
      @apply w-full py-3 text-base;
      min-height: 48px;
    }

    .btn-secondary {
      @apply w-full py-3 text-base;
      min-height: 48px;
    }

    /* Larger touch targets on mobile */
    .input-field {
      @apply py-3 text-base;
      min-height: 48px;
    }

    /* Mobile typography */
    h1 { @apply text-2xl; }
    h2 { @apply text-xl; }
    h3 { @apply text-lg; }

    /* Mobile spacing */
    .container {
      @apply px-4;
    }
    .responsive-input-group input {
    width: 100% !important;
    min-width: 0 !important;
    margin-top: 0 !important;
  }
  .harga-input-mobile-mt {
    margin-top: 0 !important;
  }
  @media (max-width: 600px) {
    .harga-input-mobile-mt {
      margin-top: 8px !important;
    }
  }
    .responsive-input {
      font-size: 15px !important;
      padding: 8px 10px !important;
    }
  }

  /* Tablet styles */
  @media (min-width: 769px) and (max-width: 1024px) {
    .btn-primary, .btn-secondary {
      @apply py-2.5;
    }
  }

  /* Desktop styles */
  @media (min-width: 1025px) {
    .btn-primary, .btn-secondary {
      @apply py-2;
    }
  }

  /* Dialog System Styles */
  .dialog-backdrop {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  .dialog-enter {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }

  .dialog-enter-active {
    opacity: 1;
    transform: scale(1) translateY(0);
    transition: all 300ms ease-out;
  }

  .dialog-exit {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  .dialog-exit-active {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    transition: all 200ms ease-in;
  }

  /* Dialog button styles */
  .dialog-btn-danger {
    @apply inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border: none;
  }

  .dialog-btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .dialog-btn-success {
    @apply inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    border: none;
  }

  .dialog-btn-success:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

/* Theme Classes */
.theme-gold {
  --color-50: var(--amber-50);
  --color-100: var(--amber-100);
  --color-200: var(--amber-200);
  --color-300: var(--amber-300);
  --color-400: var(--amber-400);
  --color-500: var(--amber-500);
  --color-600: var(--amber-600);
  --color-700: var(--amber-700);
  --color-800: var(--amber-800);
  --color-900: var(--amber-900);
}

.theme-red {
  --color-50: var(--red-50);
  --color-100: var(--red-100);
  --color-200: var(--red-200);
  --color-300: var(--red-300);
  --color-400: var(--red-400);
  --color-500: var(--red-500);
  --color-600: var(--red-600);
  --color-700: var(--red-700);
  --color-800: var(--red-800);
  --color-900: var(--red-900);
}

.theme-green {
  --color-50: var(--green-50);
  --color-100: var(--green-100);
  --color-200: var(--green-200);
  --color-300: var(--green-300);
  --color-400: var(--green-400);
  --color-500: var(--green-500);
  --color-600: var(--green-600);
  --color-700: var(--green-700);
  --color-800: var(--green-800);
  --color-900: var(--green-900);
}

.theme-blue {
  --color-50: var(--blue-50);
  --color-100: var(--blue-100);
  --color-200: var(--blue-200);
  --color-300: var(--blue-300);
  --color-400: var(--blue-400);
  --color-500: var(--blue-500);
  --color-600: var(--blue-600);
  --color-700: var(--blue-700);
  --color-800: var(--blue-800);
  --color-900: var(--blue-900);
}

.theme-black {
  --color-50: var(--black-50);
  --color-100: var(--black-100);
  --color-200: var(--black-200);
  --color-300: var(--black-300);
  --color-400: var(--black-400);
  --color-500: var(--black-500);
  --color-600: var(--black-600);
  --color-700: var(--black-700);
  --color-800: var(--black-800);
  --color-900: var(--black-900);
}

.theme-purple {
  --color-50: var(--purple-50);
  --color-100: var(--purple-100);
  --color-200: var(--purple-200);
  --color-300: var(--purple-300);
  --color-400: var(--purple-400);
  --color-500: var(--purple-500);
  --color-600: var(--purple-600);
  --color-700: var(--purple-700);
  --color-800: var(--purple-800);
  --color-900: var(--purple-900);
}

.theme-pink {
  --color-50: var(--pink-50);
  --color-100: var(--pink-100);
  --color-200: var(--pink-200);
  --color-300: var(--pink-300);
  --color-400: var(--pink-400);
  --color-500: var(--pink-500);
  --color-600: var(--pink-600);
  --color-700: var(--pink-700);
  --color-800: var(--pink-800);
  --color-900: var(--pink-900);
}

.theme-gray {
  --color-50: var(--gray-50);
  --color-100: var(--gray-100);
  --color-200: var(--gray-200);
  --color-300: var(--gray-300);
  --color-400: var(--gray-400);
  --color-500: var(--gray-500);
  --color-600: var(--gray-600);
  --color-700: var(--gray-700);
  --color-800: var(--gray-800);
  --color-900: var(--gray-900);
}
