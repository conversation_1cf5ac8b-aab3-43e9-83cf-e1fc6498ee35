{"name": "roti-ragil-invoice", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.1", "@supabase/supabase-js": "^2.50.4", "bootstrap": "^5.3.7", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-select": "^5.10.1", "recharts": "^2.15.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0"}}