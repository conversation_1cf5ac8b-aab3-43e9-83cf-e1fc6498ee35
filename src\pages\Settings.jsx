import React, { useState, useEffect } from 'react';
import ThemeSelector from '../components/ThemeSelector';
import FontSwitcher from '../components/FontSwitcher';
import ModernButton from '../components/ModernButton';

const FONT_STORAGE_KEY = 'roti-ragil-font-family';

export default function Settings() {
  const [fontFamily, setFontFamily] = useState(() => {
    return localStorage.getItem(FONT_STORAGE_KEY) || 'poppins';
  });
  const [theme, setTheme] = useState(() => {
    return localStorage.getItem('app-theme') || 'black';
  });
  const [saved, setSaved] = useState(false);

  // Do not auto-apply on change, only on save
  const handleSave = () => {
    // Save font
    document.body.classList.remove('font-inter', 'font-nunito', 'font-poppins', 'font-quicksand', 'font-worksans');
    document.body.classList.add(`font-${fontFamily}`);
    localStorage.setItem(FONT_STORAGE_KEY, fontFamily);
    // Save theme
    localStorage.setItem('app-theme', theme);
    if (window.handleThemeChange) {
      window.handleThemeChange(theme);
    }
    setSaved(true);
    setTimeout(() => setSaved(false), 1800);
  };

  return (
    <div className="max-w-lg mx-auto mt-8">
      <h1 className="text-2xl font-bold mb-6 text-orange-600 text-center">Settings</h1>
      <FontSwitcher currentFont={fontFamily} onChange={setFontFamily} />
      <div className="flex justify-end mt-6">
        <ModernButton onClick={handleSave} color="primary" style={{ minWidth: 120, fontWeight: 600 }}>
          Simpan
        </ModernButton>
      </div>
      {saved && <div className="text-green-600 text-sm text-right mt-2">Pengaturan disimpan!</div>}
    </div>
  );
}

