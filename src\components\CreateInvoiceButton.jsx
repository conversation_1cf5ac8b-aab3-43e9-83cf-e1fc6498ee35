import React from 'react';
import { useNavigate } from 'react-router-dom';
import ModernButton from './ModernButton';
import { ModernPlusIcon, ModernDocumentIcon, ModernZapIcon, ModernCreateIcon, ModernInvoiceIcon } from './ModernIcons';

const iconMap = {
  plus: ModernPlusIcon,
  file: ModernDocumentIcon,
  zap: ModernZapIcon,
  create: ModernCreateIcon,
  invoice: ModernInvoiceIcon,
};

const CreateInvoiceButton = ({
  size = 'medium',
  className = '',
  showIcon = true,
  iconType = 'plus',
  onClick,
  ...props
}) => {
  const navigate = useNavigate();
  const Icon = iconMap[iconType] || ModernPlusIcon;
  let buttonSize = {};
  if (size === 'small') buttonSize = { fontSize: 14, minHeight: 32, padding: '6px 14px' };
  if (size === 'large') buttonSize = { fontSize: 16, minHeight: 48, padding: '12px 28px' };

  return (
    <ModernButton
      startIcon={showIcon ? <Icon style={{ fontSize: buttonSize.fontSize ? buttonSize.fontSize + 2 : 20 }} /> : null}
      onClick={onClick ? onClick : () => navigate('/invoices/create')}
      style={buttonSize}
      className={className}
      {...props}
    >
      Buat Invoice
    </ModernButton>
  );
};

export default CreateInvoiceButton;
