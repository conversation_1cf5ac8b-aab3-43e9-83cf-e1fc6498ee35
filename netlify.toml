[build]
  # Build command
  command = "npm run build"
  # Output directory
  publish = "dist"

[[redirects]]
  # Handle SPA routing - redirect all routes to index.html
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  # Cache static assets
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  # Cache icons and manifest
  for = "/*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********"

[[headers]]
  # Cache manifest and service worker with shorter cache
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  # Service worker should not be cached
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"

# PWA settings
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
