import React from 'react'

const WheatIcon = ({ className = "h-8 w-8" }) => (
  <svg
    className={className}
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="50" cy="50" r="48" fill="currentColor" stroke="currentColor" strokeWidth="4"/>
    {/* Ikon roti sederhana dengan warna tema */}
    <g>
      <ellipse cx="50" cy="62" rx="22" ry="12" style={{ fill: 'var(--theme-accent)' }}/>
      <ellipse cx="50" cy="54" rx="16" ry="8" style={{ fill: 'var(--theme-secondary)' }}/>
      <path d="M30 62 Q34 48 50 48 Q66 48 70 62" style={{ stroke: 'var(--theme-primary)' }} strokeWidth="2" fill="none"/>
      <ellipse cx="42" cy="60" rx="1.2" ry="2" style={{ fill: 'var(--theme-primary)' }}/>
      <ellipse cx="50" cy="61" rx="1.2" ry="2" style={{ fill: 'var(--theme-primary)' }}/>
      <ellipse cx="58" cy="60" rx="1.2" ry="2" style={{ fill: 'var(--theme-primary)' }}/>
    </g>
  </svg>
)

export default WheatIcon
