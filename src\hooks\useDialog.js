import { useState } from 'react'

export const useDialog = () => {
  const [dialogState, setDialogState] = useState({
    isOpen: false,
    title: '',
    message: '',
    type: 'confirm',
    confirmText: 'Ya',
    cancelText: 'Batal',
    onConfirm: null,
    onCancel: null,
    showCancel: true,
    confirmButtonStyle: 'primary',
    size: 'medium'
  })

  const openDialog = (options) => {
    setDialogState({
      isOpen: true,
      title: options.title || '',
      message: options.message || '',
      type: options.type || 'confirm',
      confirmText: options.confirmText || 'Ya',
      cancelText: options.cancelText || 'Batal',
      onConfirm: options.onConfirm || null,
      onCancel: options.onCancel || null,
      showCancel: options.showCancel !== false,
      confirmButtonStyle: options.confirmButtonStyle || 'primary',
      size: options.size || 'medium'
    })
  }

  const closeDialog = () => {
    setDialogState(prev => ({ ...prev, isOpen: false }))
  }

  // Convenience methods for different dialog types
  const confirm = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'confirm',
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })
  }

  const alert = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'alert',
        showCancel: false,
        confirmText: 'OK',
        onConfirm: () => resolve(true)
      })
    })
  }

  const success = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'success',
        showCancel: false,
        confirmText: 'OK',
        confirmButtonStyle: 'success',
        onConfirm: () => resolve(true)
      })
    })
  }

  const error = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'error',
        showCancel: false,
        confirmText: 'OK',
        confirmButtonStyle: 'danger',
        onConfirm: () => resolve(true)
      })
    })
  }

  const info = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'info',
        showCancel: false,
        confirmText: 'OK',
        onConfirm: () => resolve(true)
      })
    })
  }

  const deleteConfirm = (options) => {
    return new Promise((resolve) => {
      openDialog({
        ...options,
        type: 'confirm',
        confirmText: 'Hapus',
        cancelText: 'Batal',
        confirmButtonStyle: 'danger',
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })
  }

  return {
    dialogState,
    openDialog,
    closeDialog,
    confirm,
    alert,
    success,
    error,
    info,
    deleteConfirm
  }
}

export default useDialog
