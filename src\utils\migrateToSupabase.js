// src/utils/migrateToSupabase.js
import { supabase } from '../services/supabaseClient'

// Helper to migrate a single key
const migrateKey = async (key, table) => {
  const raw = localStorage.getItem(key)
  if (!raw) return { migrated: false, count: 0 }
  let data
  try {
    data = JSON.parse(raw)
  } catch {
    return { migrated: false, count: 0 }
  }
  if (!Array.isArray(data) || data.length === 0) return { migrated: false, count: 0 }
  // Insert all data to Supabase
  const { error } = await supabase.from(table).insert(data)
  if (error) {
    console.error(`Error migrating ${key} to ${table}:`, error)
    return { migrated: false, count: 0 }
  }
  return { migrated: true, count: data.length }
}

export const migrateAllToSupabase = async () => {
  const results = {}
  results.products = await migrateKey('roti_ragil_products', 'products')
  results.customers = await migrateKey('roti_ragil_customers', 'customers')
  results.invoices = await migrateKey('roti_ragil_invoices', 'invoices')
  // Add more keys/tables as needed
  return results
}

// Usage example (call this from a button or admin page):
// import { migrateAllToSupabase } from '../utils/migrateToSupabase'
// migrateAllToSupabase().then(console.log)
