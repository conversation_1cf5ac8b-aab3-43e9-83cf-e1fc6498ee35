// Error handling utilities to prevent common runtime errors

/**
 * Safely access nested object properties
 * @param {Object} obj - The object to access
 * @param {string} path - Dot-separated path to the property
 * @param {*} defaultValue - Default value if property doesn't exist
 * @returns {*} The property value or default value
 */
export const safeGet = (obj, path, defaultValue = null) => {
  try {
    if (!obj || typeof obj !== 'object') return defaultValue;
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' && key in current 
        ? current[key] 
        : defaultValue;
    }, obj);
  } catch (error) {
    console.warn(`Error accessing path "${path}":`, error);
    return defaultValue;
  }
};

/**
 * Ensure a value is an array
 * @param {*} value - The value to check
 * @returns {Array} An array (original if already array, empty array if null/undefined, wrapped in array otherwise)
 */
export const ensureArray = (value) => {
  if (Array.isArray(value)) return value;
  if (value === null || value === undefined) return [];
  return [value];
};

/**
 * Safely parse a number
 * @param {*} value - The value to parse
 * @param {number} defaultValue - Default value if parsing fails
 * @returns {number} Parsed number or default value
 */
export const safeParseNumber = (value, defaultValue = 0) => {
  if (typeof value === 'number' && !isNaN(value)) return value;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Safely parse an integer
 * @param {*} value - The value to parse
 * @param {number} defaultValue - Default value if parsing fails
 * @returns {number} Parsed integer or default value
 */
export const safeParseInt = (value, defaultValue = 0) => {
  if (typeof value === 'number' && Number.isInteger(value)) return value;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Validate that an object has required properties
 * @param {Object} obj - The object to validate
 * @param {Array<string>} requiredProps - Array of required property names
 * @returns {boolean} True if all required properties exist
 */
export const hasRequiredProps = (obj, requiredProps) => {
  if (!obj || typeof obj !== 'object') return false;
  return requiredProps.every(prop => prop in obj);
};

/**
 * Create a safe version of a function that catches errors
 * @param {Function} fn - The function to make safe
 * @param {*} defaultReturn - Default return value on error
 * @returns {Function} Safe version of the function
 */
export const makeSafe = (fn, defaultReturn = null) => {
  return (...args) => {
    try {
      return fn(...args);
    } catch (error) {
      console.error('Safe function error:', error);
      return defaultReturn;
    }
  };
};

/**
 * Validate HPP record structure
 * @param {Object} record - The HPP record to validate
 * @returns {boolean} True if record has valid structure
 */
export const validateHPPRecord = (record) => {
  if (!record || typeof record !== 'object') return false;
  
  // Check for required fields
  const hasBasicFields = 'id' in record;
  
  // Validate ingredients array if present
  if ('ingredients' in record) {
    const ingredients = ensureArray(record.ingredients);
    const validIngredients = ingredients.every(ingredient => 
      ingredient && typeof ingredient === 'object' && 'ingredientId' in ingredient
    );
    if (!validIngredients) return false;
  }
  
  return hasBasicFields;
};

/**
 * Sanitize HPP record to ensure it has safe structure
 * @param {Object} record - The HPP record to sanitize
 * @returns {Object} Sanitized HPP record
 */
export const sanitizeHPPRecord = (record) => {
  if (!record || typeof record !== 'object') {
    return {
      id: null,
      productName: 'Unknown Product',
      ingredients: [],
      hpp: 0
    };
  }
  
  return {
    id: record.id || null,
    productName: record.productName || 'Unknown Product',
    ingredients: ensureArray(record.ingredients).map(ingredient => ({
      ingredientId: ingredient?.ingredientId || null,
      quantity: safeParseNumber(ingredient?.quantity, 0),
      unit: ingredient?.unit || 'g',
      harga: safeParseNumber(ingredient?.harga, 0)
    })),
    hpp: safeParseNumber(record.hpp, 0),
    laborCost: safeParseNumber(record.laborCost, 0),
    utilitiesCost: safeParseNumber(record.utilitiesCost, 0),
    packagingCost: safeParseNumber(record.packagingCost, 0),
    otherCosts: safeParseNumber(record.otherCosts, 0)
  };
};
