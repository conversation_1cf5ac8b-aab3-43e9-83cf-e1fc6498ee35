import React, { createContext, useContext } from 'react'
import Dialog from '../components/Dialog'
import useDialog from '../hooks/useDialog'

const DialogContext = createContext()

export const useGlobalDialog = () => {
  const context = useContext(DialogContext)
  if (!context) {
    throw new Error('useGlobalDialog must be used within a DialogProvider')
  }
  return context
}

export const DialogProvider = ({ children }) => {
  const dialogMethods = useDialog()

  return (
    <DialogContext.Provider value={dialogMethods}>
      {children}
      
      {/* Global Dialog Component */}
      <Dialog
        isOpen={dialogMethods.dialogState.isOpen}
        onClose={dialogMethods.closeDialog}
        title={dialogMethods.dialogState.title}
        message={dialogMethods.dialogState.message}
        type={dialogMethods.dialogState.type}
        confirmText={dialogMethods.dialogState.confirmText}
        cancelText={dialogMethods.dialogState.cancelText}
        onConfirm={dialogMethods.dialogState.onConfirm}
        onCancel={dialogMethods.dialogState.onCancel}
        showCancel={dialogMethods.dialogState.showCancel}
        confirmButtonStyle={dialogMethods.dialogState.confirmButtonStyle}
        size={dialogMethods.dialogState.size}
      />
    </DialogContext.Provider>
  )
}

export default DialogProvider
