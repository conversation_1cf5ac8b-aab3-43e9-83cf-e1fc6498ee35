import React, { useState, useEffect } from 'react';
import { themes, getCurrentTheme, saveTheme } from '../services/themeService';

const ThemeSelector = () => {
  const [theme, setTheme] = useState(getCurrentTheme());

  useEffect(() => {
    setTheme(getCurrentTheme());
  }, []);

  const handleChange = (e) => {
    const newTheme = e.target.value;
    setTheme(newTheme);
    saveTheme(newTheme);
    if (window.handleThemeChange) {
      window.handleThemeChange(newTheme);
    }
  };

  return (
    <select value={theme} onChange={handleChange} style={{ padding: '4px 8px', borderRadius: 6, fontWeight: 500 }}>
      {Object.entries(themes).map(([key, t]) => (
        <option
          key={key}
          value={key}
          style={{ backgroundColor: t.primary, color: '#fff' }}
        >
          {t.icon ? t.icon + ' ' : ''}{t.name || key}
        </option>
      ))}
    </select>
  );
};

export default ThemeSelector;
