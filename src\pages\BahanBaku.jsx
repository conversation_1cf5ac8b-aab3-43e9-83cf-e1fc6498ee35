import React, { useState, useEffect } from "react";
import { Plus, Search, Edit2, Trash2 } from "lucide-react";
import { formatRupiah } from '../utils/formatting';
import { ingredientService } from "../services/ingredientService";
import BahanBakuModal from '../components/BahanBakuModal';
import ModernButton from '../components/ModernButton';

// Format price without Rp prefix
const formatPrice = (amount) => {
  return new Intl.NumberFormat('id-ID').format(amount);
};

const BahanBaku = () => {
  const [ingredients, setIngredients] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingIngredient, setEditingIngredient] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    nama: "",
    stok: "", // use stok instead of berat
    harga: "",
    satuan: "g",
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadIngredients();
  }, []);

  const loadIngredients = async () => {
    setLoading(true);
    const allIngredients = await ingredientService.getAllIngredients();
    // Optionally filter by searchQuery
    const filteredIngredients = searchQuery
      ? allIngredients.filter(ingredient => (
          ingredient.nama.toLowerCase().includes(searchQuery.toLowerCase()) ||
          ingredient.satuan.toLowerCase().includes(searchQuery.toLowerCase())
        ))
      : allIngredients;
    setIngredients(filteredIngredients);
    setLoading(false);
  };

  const handleDeleteIngredient = (id) => {
    if (window.confirm("Are you sure you want to delete this ingredient?")) {
      ingredientService.deleteIngredient(id);
      loadIngredients();
    }
  };

  const handleEditIngredient = (ingredient) => {
    setEditingIngredient(ingredient);
    setFormData(ingredient);
    setIsModalOpen(true);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    loadIngredients();
  };

  // Available units for ingredients
  const units = [
    { value: 'g', label: 'Gram' },
    { value: 'kg', label: 'Kilogram' },
    { value: 'pcs', label: 'Pcs' },
  ];

  return (
    <div className="max-w-[100vw] overflow-hidden">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Bahan Baku</h1>
          <ModernButton
            onClick={() => setIsModalOpen(true)}
            startIcon={<Plus className="h-4 w-4 mr-2" />}
            color="primary"
            size="medium"
            style={{ fontWeight: 600, fontSize: 15, borderRadius: 10 }}
          >
            Tambah Bahan Baku
          </ModernButton>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari bahan baku..."
              value={searchQuery}
              onChange={handleSearch}
              className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
          </div>
        </div>

        {/* Desktop Table */}
        <div className="hidden md:block">
          <div className="table-responsive">
            <table className="table table-striped table-hover table-sm">
              <thead className="table-light">
                <tr>
                  <th scope="col" className="py-2">Nama Bahan Baku</th>
                  <th scope="col" className="py-2">Stok</th>
                  <th scope="col" className="py-2">Harga</th>
                  <th scope="col" className="text-end py-2">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {ingredients.map((ingredient) => (
                  <tr key={ingredient.id}>
                    <td className="fw-medium py-2 align-middle text-nowrap">{ingredient.nama}</td>
                    <td className="py-2 align-middle text-nowrap small">{ingredient.stok} {ingredient.satuan}</td>
                    <td className="fw-semibold text-success py-2 align-middle text-nowrap small">{formatPrice(ingredient.harga)}</td>
                    <td className="text-end py-2 align-middle">
                      <div className="d-flex justify-content-end gap-1">
                        <button
                          onClick={() => handleEditIngredient(ingredient)}
                          className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center p-1"
                          style={{ width: '28px', height: '28px' }}
                        >
                          <Edit2 style={{ width: '14px', height: '14px' }} />
                        </button>
                        <button
                          onClick={() => handleDeleteIngredient(ingredient.id)}
                          className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                          style={{ width: '28px', height: '28px' }}
                        >
                          <Trash2 style={{ width: '14px', height: '14px' }} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Table */}
        <div className="d-md-none">
          <div className="card">
            <div className="card-header bg-light">
              <h6 className="card-title mb-0 fw-semibold text-dark">
                Daftar Bahan Baku ({ingredients.length})
              </h6>
            </div>
            <div className="card-body p-0">
              {ingredients.length > 0 ? (
                <>
                  <div className="p-3 bg-light border-bottom">
                    <div className="row">
                      <div className="col-4">
                        <small className="fw-semibold text-muted text-uppercase">Nama</small>
                      </div>
                      <div className="col-3">
                        <small className="fw-semibold text-muted text-uppercase">Stok</small>
                      </div>
                      <div className="col-3">
                        <small className="fw-semibold text-muted text-uppercase">Harga</small>
                      </div>
                      <div className="col-2">
                        <small className="fw-semibold text-muted text-uppercase">Act</small>
                      </div>
                    </div>
                  </div>
                  {ingredients.map((ingredient) => (
                    <div key={ingredient.id} className="border-bottom border-light">
                      <div className="p-3">
                        <div className="row align-items-center">
                          <div className="col-4">
                            <span className="fw-medium text-dark small text-truncate d-block">{ingredient.nama}</span>
                          </div>
                          <div className="col-3">
                            <span className="text-muted small">{ingredient.stok} {ingredient.satuan}</span>
                          </div>
                          <div className="col-3">
                            <span className="fw-semibold text-success small">
                              {formatPrice(ingredient.harga)}
                            </span>
                          </div>
                          <div className="col-2">
                            <div className="d-flex gap-1 justify-content-center">
                              <button
                                onClick={() => handleEditIngredient(ingredient)}
                                className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center p-1"
                                style={{ width: '22px', height: '22px' }}
                                title="Edit"
                              >
                                <Edit2 style={{ width: '11px', height: '11px' }} />
                              </button>
                              <button
                                onClick={() => handleDeleteIngredient(ingredient.id)}
                                className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                                style={{ width: '22px', height: '22px' }}
                                title="Hapus"
                              >
                                <Trash2 style={{ width: '11px', height: '11px' }} />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              ) : (
                <div className="text-center py-5">
                  <h6 className="fw-medium text-gray-900 mb-2">Belum ada bahan baku</h6>
                  <p className="text-muted mb-0">
                    Mulai dengan menambahkan bahan baku pertama Anda.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Ingredients List */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-emerald-500"></div>
          </div>
        ) : (
          <BahanBakuModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              setEditingIngredient(null);
              setFormData({ nama: "", stok: "", harga: "", satuan: "g" });
            }}
            formData={formData}
            setFormData={setFormData}
            handleSubmit={() => {
              if (!formData.nama || !formData.stok || !formData.harga) return;
              
              const dataToSave = {
                ...formData,
                stok: parseFloat(formData.stok) || 0,
                harga: parseFloat(formData.harga) || 0
              };

              if (editingIngredient) {
                ingredientService.updateIngredient(editingIngredient.id, dataToSave);
                setEditingIngredient(null);
              } else {
                ingredientService.addIngredient(dataToSave);
              }
              
              setFormData({ nama: "", stok: "", harga: "", satuan: "g" });
              setIsModalOpen(false);
              loadIngredients();
            }}
            editingIngredient={editingIngredient}
          />
        )}
      </div>
    </div>
  );
};

export default BahanBaku;
