import React, { useEffect, useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import 'bootstrap/dist/css/bootstrap.min.css';
import Layout from './components/Layout'
import Dashboard from './pages/Dashboard'
import Products from './pages/Products'
import Customers from './pages/Customers'
import Invoices from './pages/Invoices'
import CreateInvoice from './pages/CreateInvoice'
import EditInvoice from './pages/EditInvoice'
import InvoiceDetail from './pages/InvoiceDetail'
import Reports from './pages/Reports'
import About from './pages/About'
import HPP from './pages/HPP'
import BahanBaku from './pages/BahanBaku'
import PriceList from './pages/PriceList';
import ExpenseList from './pages/ExpenseList';
import Settings from './pages/Settings'
import DataSyncNotification from './components/DataSyncNotification'
import ErrorBoundary from './components/ErrorBoundary'
import DialogProvider from './contexts/DialogContext'
import { autoSyncDefaultData } from './services/storage'

function App() {
  const [syncResult, setSyncResult] = useState(null)

  useEffect(() => {
    // Auto-sync default data when app starts
    const performSync = async () => {
      try {
        console.log('Starting auto-sync...')
        const result = await autoSyncDefaultData()
        console.log('Sync result:', result)
        setSyncResult(result)
      } catch (error) {
        console.error('Error during auto-sync:', error)
        setSyncResult({
          updated: false,
          error: true,
          message: 'Terjadi kesalahan saat sinkronisasi data'
        })
      }
    }

    performSync()
  }, [])

  const handleDismissNotification = () => {
    setSyncResult(null)
  }

  return (
    <ErrorBoundary>
      <DialogProvider>
        <Router>
          <DataSyncNotification
            syncResult={syncResult}
            onDismiss={handleDismissNotification}
          />
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/products" element={<Products />} />
              <Route path="/customers" element={<Customers />} />
              <Route path="/invoices" element={<Invoices />} />
              <Route path="/invoices/create" element={<CreateInvoice />} />
              <Route path="/invoices/:id/edit" element={<EditInvoice />} />
              <Route path="/invoices/:id" element={<InvoiceDetail />} />
              <Route path="/reports" element={<Reports />} />
              <Route path="/about" element={<About />} />
              <Route path="/bahan-baku" element={<BahanBaku />} />
              <Route path="/hpp" element={<HPP />} />
              <Route path="/price-list" element={<PriceList />} />
              <Route path="/expenses" element={<ExpenseList />} />
        <Route path="/settings" element={<Settings />} />
            </Routes>
          </Layout>
        </Router>
      </DialogProvider>
    </ErrorBoundary>
  )
}

export default App
