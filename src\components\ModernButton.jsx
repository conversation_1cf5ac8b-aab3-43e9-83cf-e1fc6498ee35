import React from 'react';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';

const StyledButton = styled(Button)(({ theme }) => ({
  background: 'var(--button-bg, var(--theme-gradient, linear-gradient(90deg, #FFD700 0%, #FFC107 100%)))',
  color: 'var(--button-color, #fff)',
  boxShadow: '0 4px 20px rgba(0,0,0,0.06), 0 1.5px 4px rgba(0,0,0,0.03)',
  fontWeight: 600,
  borderRadius: '9999px', // Fully rounded
  padding: '8px 20px',
  textTransform: 'none',
  fontSize: 15,
  minHeight: 40,
  transition: 'all 0.2s cubic-bezier(.4,2,.3,1)',
  backdropFilter: 'blur(8px) saturate(180%)',
  WebkitBackdropFilter: 'blur(8px) saturate(180%)',
  border: '1px solid rgba(255,255,255,0.18)',
  '&:hover': {
    background: 'var(--button-hover-bg, var(--button-bg, #ffe900))',
    boxShadow: '0 8px 32px 0 rgba(31,38,135,0.10)',
    transform: 'translateY(-1px)',
  },
  '&:active': {
    background: 'var(--button-active-bg, var(--button-hover-bg, #ffe900))',
    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
    transform: 'scale(0.98)',
  },
}));

const ModernButton = ({
  children,
  startIcon,
  endIcon,
  className = '',
  ...props
}) => {
  return (
    <StyledButton
      className={`glass shadow-modern ${className}`}
      startIcon={startIcon}
      endIcon={endIcon}
      disableElevation
      {...props}
    >
      {children}
    </StyledButton>
  );
};

export default ModernButton;
