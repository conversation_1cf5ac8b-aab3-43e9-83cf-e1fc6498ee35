import React from 'react'
import { useGlobalDialog } from '../contexts/DialogContext'

const DialogExamples = () => {
  const { confirm, alert, success, error, info, deleteConfirm } = useGlobalDialog()

  const handleConfirmExample = async () => {
    const result = await confirm({
      title: 'Konfirmasi Aksi',
      message: '<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan aksi ini?',
      confirmText: 'Ya, Lanjutkan',
      cancelText: 'Batal'
    })
    
    if (result) {
      console.log('User confirmed')
    } else {
      console.log('User cancelled')
    }
  }

  const handleAlertExample = async () => {
    await alert({
      title: 'Informasi',
      message: 'Ini adalah contoh dialog alert dengan informasi penting.',
      confirmText: '<PERSON><PERSON><PERSON>'
    })
  }

  const handleSuccessExample = async () => {
    await success({
      title: 'Berhasil!',
      message: '<PERSON>si telah berhasil dilakukan dengan sempurna.',
      confirmText: 'OK'
    })
  }

  const handleErrorExample = async () => {
    await error({
      title: '<PERSON><PERSON><PERSON><PERSON>',
      message: '<PERSON><PERSON>, terja<PERSON> kes<PERSON>han saat memproses permintaan Anda. Silakan coba lagi.',
      confirmText: 'OK'
    })
  }

  const handleInfoExample = async () => {
    await info({
      title: 'Tips',
      message: 'Anda dapat menggunakan keyboard shortcut Ctrl+S untuk menyimpan data dengan cepat.',
      confirmText: 'Mengerti'
    })
  }

  const handleDeleteExample = async () => {
    const result = await deleteConfirm({
      title: 'Hapus Item',
      message: 'Apakah Anda yakin ingin menghapus item ini? Tindakan ini tidak dapat dibatalkan.',
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })
    
    if (result) {
      console.log('Item deleted')
    }
  }

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Dialog Examples</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <button
          onClick={handleConfirmExample}
          className="btn-primary"
        >
          Confirm Dialog
        </button>
        
        <button
          onClick={handleAlertExample}
          className="btn-secondary"
        >
          Alert Dialog
        </button>
        
        <button
          onClick={handleSuccessExample}
          className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
        >
          Success Dialog
        </button>
        
        <button
          onClick={handleErrorExample}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Error Dialog
        </button>
        
        <button
          onClick={handleInfoExample}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Info Dialog
        </button>
        
        <button
          onClick={handleDeleteExample}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Delete Confirm
        </button>
      </div>
      
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Usage Examples:</h3>
        <pre className="text-sm text-gray-700 overflow-x-auto">
{`// Import the hook
import { useGlobalDialog } from '../contexts/DialogContext'

// In your component
const { confirm, alert, success, error, info, deleteConfirm } = useGlobalDialog()

// Confirm dialog
const result = await confirm({
  title: 'Konfirmasi',
  message: 'Apakah Anda yakin?',
  confirmText: 'Ya',
  cancelText: 'Tidak'
})

// Success notification
await success({
  title: 'Berhasil!',
  message: 'Data berhasil disimpan.'
})

// Error notification
await error({
  title: 'Error',
  message: 'Terjadi kesalahan.'
})`}
        </pre>
      </div>
    </div>
  )
}

export default DialogExamples
