import React from 'react'
import ModernButton from './ModernButton'
import { X, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'

const Dialog = ({
  isOpen,
  onClose,
  title,
  message,
  type = 'confirm', // 'confirm', 'alert', 'success', 'error', 'info', 'custom'
  confirmText = 'Ya',
  cancelText = 'Batal',
  onConfirm,
  onCancel,
  children,
  showCancel = true,
  confirmButtonStyle = 'primary', // 'primary', 'danger', 'success'
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  if (!isOpen) return null

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose?.()
    }
  }

  const handleConfirm = () => {
    onConfirm?.()
    onClose?.()
  }

  const handleCancel = () => {
    onCancel?.()
    onClose?.()
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-emerald-500" />
      case 'error':
        return <AlertCircle className="h-6 w-6 text-red-500" />
      case 'info':
        return <Info className="h-6 w-6 text-blue-500" />
      case 'confirm':
      default:
        return <AlertTriangle className="h-6 w-6 text-amber-500" />
    }
  }

  const getConfirmButtonClass = () => {
    switch (confirmButtonStyle) {
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
      case 'success':
        return 'bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500'
      case 'primary':
      default:
        return 'btn-primary'
    }
  }

  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'max-w-sm'
      case 'large':
        return 'max-w-2xl'
      case 'medium':
      default:
        return 'max-w-md'
    }
  }

  return (
    <div 
      className="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="modal-title" 
      role="dialog" 
      aria-modal="true"
    >
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity duration-300 ease-out"
        onClick={handleBackdropClick}
      ></div>

      {/* Dialog */}
      <div className="flex min-h-full items-center justify-center p-4 text-center sm:p-0">
        <div 
          className={`relative transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 ease-out ${getSizeClass()} w-full`}
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, var(--theme-50) 100%)',
            border: '1px solid var(--theme-200)'
          }}
        >
          {/* Header */}
          <div className="px-6 pt-6 pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {type !== 'custom' && (
                  <div className="flex-shrink-0">
                    {getIcon()}
                  </div>
                )}
                <div className="text-left">
                  {title && (
                    <h3 
                      className="text-lg font-semibold text-gray-900"
                      id="modal-title"
                    >
                      {title}
                    </h3>
                  )}
                </div>
              </div>
              <button
                onClick={onClose}
                className="flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
                aria-label="Tutup dialog"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 pb-6">
            {message && (
              <div className="text-sm text-gray-600 mb-6 text-left leading-relaxed">
                {message}
              </div>
            )}
            {children && (
              <div className="text-left">
                {children}
              </div>
            )}
          </div>

          {/* Actions */}
          {(onConfirm || onCancel || showCancel) && (
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0">
              {showCancel && (
                <ModernButton
                  onClick={handleCancel}
                  color="inherit"
                  style={{ minWidth: 90, marginRight: 8 }}
                >
                  {cancelText}
                </ModernButton>
              )}
              {onConfirm && (
                <ModernButton
                  onClick={handleConfirm}
                  color={confirmButtonStyle === 'danger' ? 'error' : confirmButtonStyle === 'success' ? 'success' : 'primary'}
                  style={{ minWidth: 90 }}
                >
                  {confirmText}
                </ModernButton>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dialog
