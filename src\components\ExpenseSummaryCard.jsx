import React, { useMemo } from 'react';
import { TrendingDown } from 'lucide-react';

// Helper: parse date string to Date object (YYYY-MM-DD)
function parseDate(dateStr) {
  if (!dateStr) return null;
  const [year, month, day] = dateStr.split('-').map(Number);
  return new Date(year, month - 1, day);
}

function filterExpensesByPeriod(expenses, period, customDateRange, useCustomRange) {
  if (!Array.isArray(expenses)) return [];
  const now = new Date();
  let start, end;

  if (useCustomRange && customDateRange?.start && customDateRange?.end) {
    start = parseDate(customDateRange.start);
    end = parseDate(customDateRange.end);
    end.setHours(23, 59, 59, 999);
  } else {
    switch (period) {
      case 'day':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week':
        start = new Date(now);
        start.setDate(now.getDate() - now.getDay()); // Sunday as start
        start.setHours(0, 0, 0, 0);
        end = new Date(start);
        end.setDate(start.getDate() + 7);
        break;
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      case 'year':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear() + 1, 0, 1);
        break;
      default:
        start = null;
        end = null;
    }
  }

  return expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    if (start && d < start) return false;
    if (end && d >= end) return false;
    return true;
  });
}

function formatCurrency(amount) {
  return amount?.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' }) || 'Rp0';
}

const ExpenseSummaryCard = ({ selectedPeriod, customDateRange, useCustomRange, periodOptions }) => {
  const expenses = useMemo(() => {
    try {
      const stored = localStorage.getItem('expenses');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }, []);

  const filteredExpenses = useMemo(
    () => filterExpensesByPeriod(expenses, selectedPeriod, customDateRange, useCustomRange),
    [expenses, selectedPeriod, customDateRange, useCustomRange]
  );
  const totalExpense = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

  return (
    <div className="card bg-red-50 rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <TrendingDown className="h-8 w-8 text-red-400" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-red-700">Total Pengeluaran</p>
          <p className="text-2xl font-bold text-red-700">{formatCurrency(totalExpense)}</p>
          <p className="text-xs text-red-400 mt-1">
            Periode: {useCustomRange && customDateRange?.start && customDateRange?.end
              ? `${customDateRange.start} - ${customDateRange.end}`
              : periodOptions.find(p => p.value === selectedPeriod)?.label}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ExpenseSummaryCard;
