const CURRENT_VERSION = '1.0.0';
const VERSION_CHECK_URL = '/version.json';
const PLATFORM = 'android'; // atau 'web' tergantung platform

export const checkForUpdates = async () => {
  try {
    const response = await fetch(VERSION_CHECK_URL);
    const data = await response.json();
    
    // Check if updates are enabled for this platform
    if (data.platform?.[PLATFORM]?.enabled === false) {
      return { hasUpdate: false };
    }
    
    // Compare versions using semantic versioning
    const hasUpdate = compareVersions(CURRENT_VERSION, data.version);
    const isForceUpdate = data.force || 
      compareVersions(CURRENT_VERSION, data.platform?.[PLATFORM]?.minimumVersion || '0.0.0');
    
    if (hasUpdate) {
      return {
        hasUpdate: true,
        newVersion: data.version,
        updateUrl: data.updateUrl,
        releaseNotes: data.releaseNotes,
        force: isForceUpdate,
        releaseDate: data.releaseDate
      };
    }
    
    return { hasUpdate: false };
  } catch (error) {
    console.error('Failed to check for updates:', error);
    return { hasUpdate: false, error };
  }
};

// Simple semantic version comparison
const compareVersions = (current, latest) => {
  const currentParts = current.split('.').map(Number);
  const latestParts = latest.split('.').map(Number);
  
  for (let i = 0; i < 3; i++) {
    if (latestParts[i] > currentParts[i]) return true;
    if (latestParts[i] < currentParts[i]) return false;
  }
  
  return false;
};
