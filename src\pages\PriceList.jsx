import React, { useEffect, useRef, useState } from 'react';
import html2canvas from 'html2canvas';
import { getProducts } from '../services/storage';
import ModernButton from '../components/ModernButton';

function formatCurrency(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount);
}

const PriceList = () => {
  const [products, setProducts] = useState([]);
  const listRef = useRef();

  useEffect(() => {
    setProducts(getProducts() || []);
  }, []);

  const handleDownloadPNG = async () => {
    if (!listRef.current) return;
    const canvas = await html2canvas(listRef.current, {
      backgroundColor: '#fff',
      scale: 2
    });
    const link = document.createElement('a');
    link.download = `daftar-harga-produk-roti-ragil.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();
  };

  return (
    <div className="responsive-container" style={{ maxWidth: 800, margin: '0 auto', padding: '8px 1px', width: '100%' }}>
      <h1 className="text-base font-bold mb-3 text-center" style={{ color: 'var(--theme-500)', wordBreak: 'break-word', lineHeight: 1.2 }}>Daftar Harga Produk</h1>
      <div className="w-full flex flex-col items-center mb-3" style={{ gap: 6 }}>
        <ModernButton
  onClick={handleDownloadPNG}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Download sebagai PNG
</ModernButton>
      </div>
      <div ref={listRef} className="bg-white shadow-lg rounded-lg p-2 sm:p-4 border border-gray-200" style={{ width: '100%', boxSizing: 'border-box' }}>
        {/* Export-visible heading */}
        <div className="text-center mb-4">
          <div className="text-base sm:text-3xl md:text-5xl font-extrabold tracking-wide leading-tight"
                style={{ color: 'var(--theme-500)', wordBreak: 'break-word', lineHeight: 1.1 }}>
            ROTI RAGIL
          </div>
          <div className="text-xs sm:text-2xl md:text-3xl font-light tracking-widest mt-1"
                style={{ color: 'var(--theme-600)', wordBreak: 'break-word', lineHeight: 1.1 }}>
            PRICE LIST
          </div>
        </div>
        {products && products.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4" style={{ width: '100%' }}>
            {products.map((product, idx) => (
              <div
  key={product.id || idx}
  className="flex flex-col items-center justify-center rounded-xl shadow-md hover:shadow-xl hover:scale-[1.03] transition-all p-3 sm:p-4 min-h-[90px] sm:min-h-[120px] w-full"
  style={{ background: 'var(--theme-50, #fff)', border: '1px solid var(--theme-200, #e5e7eb)', wordBreak: 'break-word' }}
>
                <div className="w-8 h-8 sm:w-12 sm:h-12 aspect-square flex items-center justify-center rounded-full mb-1 sm:mb-3 shadow-sm"
                  style={{ background: 'var(--theme-100, #fef3c7)' }}>
                  <span className="block flex items-center justify-center w-full h-full font-bold text-xs sm:text-lg leading-[1] text-center align-middle"
                        style={{ color: 'var(--theme-600, #ea580c)', wordBreak: 'break-word' }}>{idx + 1}</span>
                </div>
                <div className="text-base sm:text-lg font-semibold text-center mb-1 sm:mb-2"
  style={{ color: 'var(--theme-700, #374151)', wordBreak: 'break-word' }}>
  {product.nama}
</div>
<div className="font-bold text-base sm:text-xl text-center drop-shadow-sm"
  style={{ color: 'var(--theme-500, #f59e42)', wordBreak: 'break-word' }}>
  {formatCurrency(product.harga)}
</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-4 sm:py-12 text-center text-gray-400 text-xs sm:text-lg">Tidak ada produk tersedia.</div>
        )}
      </div>
      <div className="w-full flex flex-col items-center mt-3" style={{ gap: 6 }}>
        <ModernButton
  onClick={handleDownloadPNG}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Download sebagai PNG
</ModernButton>
      </div>
    </div>
  );
};

export default PriceList;
