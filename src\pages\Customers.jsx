import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Users } from 'lucide-react'
import { getCustomers, saveCustomer, updateCustomer, deleteCustomer } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'

const Customers = () => {
  const [customers, setCustomers] = useState([])
  const [showModal, setShowModal] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState(null)
  const [formData, setFormData] = useState({
    nama: ''
  })
  const [loading, setLoading] = useState(false)
  const { deleteConfirm, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    setLoading(true)
    const data = await getCustomers()
    setCustomers(Array.isArray(data) ? data : [])
    setLoading(false)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Add default values for removed fields
    const customerData = {
      ...formData,
      alamat: '-',
      telepon: '-',
      email: ''
    }

    try {
      if (editingCustomer) {
        updateCustomer(editingCustomer.id, customerData)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil diperbarui!'
        })
      } else {
        saveCustomer(customerData)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil ditambahkan!'
        })
      }

      setFormData({ nama: '' })
      setEditingCustomer(null)
      setShowModal(false)
      loadCustomers()
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat menyimpan pelanggan.'
      })
    }
  }

  const handleEdit = (customer) => {
    setEditingCustomer(customer)
    setFormData({
      nama: customer.nama
    })
    setShowModal(true)
  }

  const handleDelete = async (customer) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus Pelanggan',
      message: `Apakah Anda yakin ingin menghapus pelanggan "${customer.nama}"?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        deleteCustomer(customer.id)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil dihapus!'
        })
        loadCustomers()
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus pelanggan.'
        })
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl md:text-3xl font-bold text-center" style={{ color: 'var(--theme-500)' }}>
          Manajemen Pelanggan
        </h1>
        <ModernButton
  onClick={() => {
    setEditingCustomer(null)
    setFormData({ nama: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Pelanggan
</ModernButton>
      </div>

      {/* Customers List */}
      <div className="card">
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-emerald-500"></div>
          </div>
        ) : customers.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block">
              <div className="table-responsive">
                <table className="table table-striped table-hover">
                  <thead className="table-light">
                    <tr>
                      <th scope="col">Nama Pelanggan</th>
                      <th scope="col" className="text-end">Aksi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => (
                      <tr key={customer.id}>
                        <td className="fw-medium">{customer.nama}</td>
                        <td className="text-end">
                          <div className="d-flex justify-content-end gap-2">
                            <button
                              onClick={() => handleEdit(customer)}
                              className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                              style={{ width: '32px', height: '32px' }}
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(customer)}
                              className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                              style={{ width: '32px', height: '32px' }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Cards */}
            <div className="d-md-none p-3">
              {customers.map((customer) => (
                <div key={customer.id} className="card mb-3 border-light">
                  <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center">
                      <div className="flex-grow-1">
                        <h5 className="card-title fw-semibold mb-0">{customer.nama}</h5>
                      </div>
                      <div className="d-flex gap-2 ms-3">
                        <button
                          onClick={() => handleEdit(customer)}
                          className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(customer)}
                          className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada pelanggan</h3>
            <p className="mt-1 text-sm text-gray-500">
              Mulai dengan menambahkan pelanggan pertama Anda.
            </p>
            <div className="mt-6">
              <ModernButton
  onClick={() => {
    setEditingCustomer(null)
    setFormData({ nama: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Pelanggan
</ModernButton>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-2xl rounded-xl bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                {editingCustomer ? 'Edit Pelanggan' : 'Tambah Pelanggan'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nama Pelanggan</label>
                  <input
                    type="text"
                    required
                    value={formData.nama}
                    onChange={(e) => setFormData({ ...formData, nama: e.target.value })}
                    className="input-field"
                    placeholder="Masukkan nama pelanggan"
                  />
                </div>
                <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6">
                  <ModernButton
                    type="button"
                    onClick={() => setShowModal(false)}
                    color="inherit"
                    fullWidth
                    style={{ minWidth: 90 }}
                  >
                    Batal
                  </ModernButton>
                  <ModernButton
                    type="submit"
                    color="primary"
                    fullWidth
                    style={{ minWidth: 90 }}
                  >
                    {editingCustomer ? 'Update' : 'Simpan'}
                  </ModernButton>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Customers
