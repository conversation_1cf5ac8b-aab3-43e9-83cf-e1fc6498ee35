// Theme configurations
export const themes = {
  'yellow-charcoal': {
    name: 'Yellow & Charcoal',
    primary: '#FEE715', // Yellow
    secondary: '#101820', // Charcoal
    sidebarBg: '#101820',
    buttonBg: '#FEE715',
    buttonColor: '#101820',
    buttonHoverBg: '#ffe900',
    buttonActiveBg: '#ffe900',
    icon: '🌞',
  },
  'dark': {
    name: 'Dark',
    primary: '#23272f',
    secondary: '#f5f5f5',
    sidebarBg: '#181a1b',
    buttonBg: '#23272f',
    buttonColor: '#f5f5f5',
    buttonHoverBg: '#2c313a',
    buttonActiveBg: '#1a1d22',
    icon: '🌑',
  },
  'light': {
    name: 'Light',
    primary: '#f5f5f5',
    secondary: '#23272f',
    sidebarBg: '#ffffff',
    buttonBg: '#e0e0e0',
    buttonColor: '#23272f',
    buttonHoverBg: '#f0f0f0',
    buttonActiveBg: '#e0e0e0',
    icon: '🌤️',
  },
  'blue': {
    name: 'Blue',
    primary: '#2196f3',
    secondary: '#0d47a1',
    sidebarBg: '#1565c0',
    buttonBg: '#2196f3',
    buttonColor: '#fff',
    buttonHoverBg: '#42a5f5',
    buttonActiveBg: '#1976d2',
    icon: '💧',
  },
  'green': {
    name: 'Green',
    primary: '#4caf50',
    secondary: '#1b5e20',
    sidebarBg: '#388e3c',
    buttonBg: '#4caf50',
    buttonColor: '#fff',
    buttonHoverBg: '#66bb6a',
    buttonActiveBg: '#2e7d32',
    icon: '🌱',
  },
  'pink': {
    name: 'Pink',
    primary: '#EC4899', // Fresh pink
    secondary: '#BE185D', // Darker pink
    sidebarBg: '#F472B6',
    buttonBg: '#EC4899',
    buttonColor: '#fff',
    buttonHoverBg: '#F9A8D4',
    buttonActiveBg: '#BE185D',
    icon: '🌸',
  },
};

export const defaultTheme = 'yellow-charcoal';

// Get current theme from localStorage or use default
export const getCurrentTheme = () => {
  const saved = localStorage.getItem('app-theme');
  return saved || defaultTheme;
};

// Save theme to localStorage
export const saveTheme = (theme) => {
  localStorage.setItem('app-theme', theme);
};

// Get theme configuration
export const getThemeConfig = (themeName) => {
  return themes[themeName] || themes[defaultTheme];
};
